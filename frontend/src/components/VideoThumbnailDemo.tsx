import React, { useState, useCallback } from "react";
import {
  Box,
  Typography,
  TextField,
  Button,
  Paper,
  Slider,
  FormControlLabel,
  Switch,
  Alert,
  Divider,
} from "@mui/material";
import { VideoThumbnailStrip } from "./VideoThumbnailStrip";

/**
 * 视频缩略图演示组件
 * 用于测试和展示视频缩略图功能
 */
export const VideoThumbnailDemo: React.FC = () => {
  const [videoUrl, setVideoUrl] = useState(
    "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4"
  );
  const [thumbnailCount, setThumbnailCount] = useState(5);
  const [stripWidth, setStripWidth] = useState(800);
  const [stripHeight, setStripHeight] = useState(90);
  const [showProgress, setShowProgress] = useState(true);
  const [currentElement, setCurrentElement] = useState<any>(null);
  const [clickedInfo, setClickedInfo] = useState<string>("");

  // 创建视频元素对象
  const createVideoElement = useCallback(() => {
    if (!videoUrl.trim()) {
      setCurrentElement(null);
      return;
    }

    const element = {
      id: `video-${Date.now()}`,
      type: "video",
      properties: {
        src: videoUrl,
        elementId: `video-element-${Date.now()}`,
      },
      timeFrame: {
        start: 0,
        end: 10000, // 10秒
      },
    };

    setCurrentElement(element);
  }, [videoUrl]);

  // 处理缩略图点击
  const handleThumbnailClick = useCallback(
    (index: number, timePercent: number) => {
      setClickedInfo(
        `点击了第 ${index + 1} 个缩略图，时间位置: ${timePercent.toFixed(1)}%`
      );
    },
    []
  );

  // 预设视频URL
  const presetVideos = [
    {
      name: "Big Buck Bunny (示例视频)",
      url: "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4",
    },
    {
      name: "Elephant Dream",
      url: "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4",
    },
    {
      name: "For Bigger Blazes",
      url: "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4",
    },
  ];

  return (
    <Box sx={{ p: 3, maxWidth: 1200, mx: "auto" }}>
      <Typography variant="h4" gutterBottom>
        视频缩略图演示
      </Typography>
      
      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        这个演示展示了类似 YouTube 的视频缩略图功能。输入视频URL，调整参数，查看效果。
      </Typography>

      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          配置参数
        </Typography>

        {/* 视频URL输入 */}
        <TextField
          fullWidth
          label="视频URL"
          value={videoUrl}
          onChange={(e) => setVideoUrl(e.target.value)}
          sx={{ mb: 2 }}
          placeholder="输入视频URL..."
        />

        {/* 预设视频按钮 */}
        <Box sx={{ mb: 2 }}>
          <Typography variant="subtitle2" gutterBottom>
            或选择预设视频:
          </Typography>
          <Box sx={{ display: "flex", gap: 1, flexWrap: "wrap" }}>
            {presetVideos.map((preset, index) => (
              <Button
                key={index}
                variant="outlined"
                size="small"
                onClick={() => setVideoUrl(preset.url)}
              >
                {preset.name}
              </Button>
            ))}
          </Box>
        </Box>

        <Divider sx={{ my: 2 }} />

        {/* 缩略图数量 */}
        <Box sx={{ mb: 2 }}>
          <Typography gutterBottom>
            缩略图数量: {thumbnailCount}
          </Typography>
          <Slider
            value={thumbnailCount}
            onChange={(_, value) => setThumbnailCount(value as number)}
            min={3}
            max={10}
            step={1}
            marks
            valueLabelDisplay="auto"
          />
        </Box>

        {/* 宽度设置 */}
        <Box sx={{ mb: 2 }}>
          <Typography gutterBottom>
            缩略图条宽度: {stripWidth}px
          </Typography>
          <Slider
            value={stripWidth}
            onChange={(_, value) => setStripWidth(value as number)}
            min={400}
            max={1200}
            step={50}
            valueLabelDisplay="auto"
          />
        </Box>

        {/* 高度设置 */}
        <Box sx={{ mb: 2 }}>
          <Typography gutterBottom>
            缩略图条高度: {stripHeight}px
          </Typography>
          <Slider
            value={stripHeight}
            onChange={(_, value) => setStripHeight(value as number)}
            min={60}
            max={150}
            step={10}
            valueLabelDisplay="auto"
          />
        </Box>

        {/* 显示进度开关 */}
        <FormControlLabel
          control={
            <Switch
              checked={showProgress}
              onChange={(e) => setShowProgress(e.target.checked)}
            />
          }
          label="显示加载进度"
          sx={{ mb: 2 }}
        />

        {/* 生成按钮 */}
        <Button
          variant="contained"
          onClick={createVideoElement}
          disabled={!videoUrl.trim()}
          sx={{ mt: 1 }}
        >
          生成缩略图
        </Button>
      </Paper>

      {/* 缩略图展示区域 */}
      {currentElement && (
        <Paper sx={{ p: 3, mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            缩略图预览
          </Typography>
          
          <Box sx={{ display: "flex", justifyContent: "center", mb: 2 }}>
            <VideoThumbnailStrip
              element={currentElement}
              thumbnailCount={thumbnailCount}
              width={stripWidth}
              height={stripHeight}
              showProgress={showProgress}
              onThumbnailClick={handleThumbnailClick}
            />
          </Box>

          {clickedInfo && (
            <Alert severity="info" sx={{ mt: 2 }}>
              {clickedInfo}
            </Alert>
          )}
        </Paper>
      )}

      {/* 使用说明 */}
      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>
          功能说明
        </Typography>
        
        <Typography variant="body2" component="div">
          <ul>
            <li>支持生成多个视频缩略图，均匀分布在视频时长中</li>
            <li>缩略图支持悬停效果和点击交互</li>
            <li>自动缓存生成的缩略图，避免重复计算</li>
            <li>支持CORS和非CORS视频源</li>
            <li>显示加载进度和错误状态</li>
            <li>响应式设计，支持自定义尺寸</li>
            <li>类似YouTube的视觉效果和交互体验</li>
          </ul>
        </Typography>
      </Paper>
    </Box>
  );
};

export default VideoThumbnailDemo;
